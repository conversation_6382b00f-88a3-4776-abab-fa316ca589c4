'use client';
import React, { useState } from 'react';

export default function FlipBook() {
  const [currentPage, setCurrentPage] = useState(0);
  const [isBookOpen, setIsBookOpen] = useState(false);
  const [isFlipping, setIsFlipping] = useState(false);
  const [flipDirection, setFlipDirection] = useState(null); // 'next' or 'prev'

  // Sample pages data
  const pages = [
    {
      id: 'cover',
      type: 'cover',
      content: {
        title: 'My Diary',
        subtitle: 'A Journey Through Time',
        image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=600&fit=crop'
      }
    },
    {
      id: 1,
      type: 'content',
      content: {
        title: 'Chapter 1: The Beginning',
        text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
        pageNumber: 1
      }
    },
    {
      id: 2,
      type: 'content',
      content: {
        title: 'Chapter 2: The Journey',
        text: 'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.',
        pageNumber: 2
      }
    },
    {
      id: 3,
      type: 'content',
      content: {
        title: 'Chapter 3: The Discovery',
        text: 'Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.',
        pageNumber: 3
      }
    },
    {
      id: 4,
      type: 'content',
      content: {
        title: 'Chapter 4: The Resolution',
        text: 'Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur.',
        pageNumber: 4
      }
    },
    {
      id: 5,
      type: 'content',
      content: {
        title: 'The End',
        text: 'Thank you for reading this wonderful journey through the pages of time. Every story has an ending, but every ending is also a new beginning. The memories we create and the stories we tell become part of who we are, shaping our future adventures.',
        pageNumber: 5
      }
    }
  ];

  const openBook = () => {
    setIsBookOpen(true);
    setCurrentPage(1);
  };

  const nextPage = () => {
    if (currentPage < pages.length - 2 && !isFlipping) {
      setIsFlipping(true);
      setFlipDirection('next');
      setTimeout(() => {
        setCurrentPage(currentPage + 2);
        setIsFlipping(false);
        setFlipDirection(null);
      }, 800);
    }
  };

  const prevPage = () => {
    if (currentPage > 1 && !isFlipping) {
      setIsFlipping(true);
      setFlipDirection('prev');
      setTimeout(() => {
        setCurrentPage(currentPage - 2);
        setIsFlipping(false);
        setFlipDirection(null);
      }, 800);
    }
  };

  const getCurrentLeftPage = () => {
    if (!isBookOpen) return null;
    return pages[currentPage];
  };

  const getCurrentRightPage = () => {
    if (!isBookOpen) return null;
    return pages[currentPage + 1];
  };

  return (
    <>
      {/* Custom CSS for 3D Page Flipping */}
      <style jsx>{`
        .flip-page-right {
          animation: flipPageRight 0.8s ease-in-out forwards;
        }

        .flip-page-left {
          animation: flipPageLeft 0.8s ease-in-out forwards;
        }

        @keyframes flipPageRight {
          0% {
            transform: rotateY(0deg);
            z-index: 10;
          }
          50% {
            transform: rotateY(-90deg);
            z-index: 10;
          }
          100% {
            transform: rotateY(-180deg);
            z-index: 1;
          }
        }

        @keyframes flipPageLeft {
          0% {
            transform: rotateY(-180deg);
            z-index: 1;
          }
          50% {
            transform: rotateY(-90deg);
            z-index: 10;
          }
          100% {
            transform: rotateY(0deg);
            z-index: 10;
          }
        }

        .page-shadow {
          box-shadow:
            0 0 20px rgba(0,0,0,0.1),
            inset 0 0 20px rgba(0,0,0,0.05),
            0 10px 30px rgba(0,0,0,0.2);
        }

        .page-shadow-left {
          box-shadow:
            -8px 0 20px rgba(0,0,0,0.15),
            inset 8px 0 15px rgba(0,0,0,0.08),
            0 10px 30px rgba(0,0,0,0.1);
        }

        .page-shadow-right {
          box-shadow:
            8px 0 20px rgba(0,0,0,0.15),
            inset -8px 0 15px rgba(0,0,0,0.08),
            0 10px 30px rgba(0,0,0,0.1);
        }
      `}</style>

      <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center p-4">
        <div className="relative">
        {/* Book Container */}
        <div
          className={`relative ${
            isBookOpen ? 'w-[90vw] max-w-[900px] h-[70vh] max-h-[600px]' : 'w-[45vw] max-w-[450px] h-[70vh] max-h-[600px]'
          }`}
          style={{
            perspective: '2000px',
            transition: isBookOpen ? 'width 1s ease-in-out, height 1s ease-in-out' : 'none'
          }}
        >
          
          {/* Cover Page */}
          {!isBookOpen && (
            <div
              className="absolute inset-0 bg-gradient-to-br from-amber-800 to-amber-900 rounded-r-lg shadow-2xl cursor-pointer hover:scale-105"
              onClick={openBook}
              style={{
                transformStyle: 'preserve-3d',
                boxShadow: '15px 15px 40px rgba(0,0,0,0.4), inset -8px 0 15px rgba(0,0,0,0.3)',
                transition: 'transform 0.3s ease-in-out'
              }}
            >
              <div className="absolute inset-0 bg-cover bg-center rounded-r-lg opacity-20" 
                   style={{ backgroundImage: `url(${pages[0].content.image})` }}></div>
              <div className="relative h-full flex flex-col items-center justify-center text-white p-8">
                <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 text-center font-serif">
                  {pages[0].content.title}
                </h1>
                <p className="text-lg md:text-2xl text-center opacity-90 font-light">
                  {pages[0].content.subtitle}
                </p>
                <div className="mt-8 text-sm opacity-70">Click to open</div>
              </div>
              {/* Book spine effect */}
              <div className="absolute left-0 top-0 w-3 h-full bg-amber-900 rounded-l-sm shadow-inner"></div>
              {/* Book thickness effect */}
              <div className="absolute -right-1 top-1 w-2 h-full bg-amber-700 rounded-r-sm" style={{ transform: 'skewY(1deg)' }}></div>
            </div>
          )}

          {/* Open Book */}
          {isBookOpen && (
            <div className="relative w-full h-full">
              
              {/* Left Page */}
              <div
                className={`absolute left-0 top-0 w-1/2 h-full bg-white rounded-l-lg page-shadow-left ${
                  isFlipping && flipDirection === 'prev' ? 'flip-page-left' : ''
                }`}
                style={{
                  transformOrigin: 'right center',
                  transformStyle: 'preserve-3d',
                  background: 'linear-gradient(to right, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                  zIndex: isFlipping && flipDirection === 'prev' ? 10 : 2
                }}
              >
                {getCurrentLeftPage() && (
                  <div className="p-6 md:p-8 h-full flex flex-col">
                    <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                      {getCurrentLeftPage().content.title}
                    </h2>
                    <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                      {getCurrentLeftPage().content.text}
                    </p>
                    <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                      {getCurrentLeftPage().content.pageNumber}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Page */}
              <div
                className={`absolute right-0 top-0 w-1/2 h-full bg-white rounded-r-lg page-shadow-right ${
                  isFlipping && flipDirection === 'next' ? 'flip-page-right' : ''
                }`}
                style={{
                  transformOrigin: 'left center',
                  transformStyle: 'preserve-3d',
                  background: 'linear-gradient(to left, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                  zIndex: isFlipping && flipDirection === 'next' ? 10 : 2
                }}
              >
                {getCurrentRightPage() && (
                  <div className="p-6 md:p-8 h-full flex flex-col">
                    <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                      {getCurrentRightPage().content.title}
                    </h2>
                    <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                      {getCurrentRightPage().content.text}
                    </p>
                    <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                      {getCurrentRightPage().content.pageNumber}
                    </div>
                  </div>
                )}
              </div>

              {/* Next Pages (shown during flip) */}
              {isFlipping && flipDirection === 'next' && currentPage + 2 < pages.length && (
                <>
                  {/* Next Left Page */}
                  <div
                    className="absolute left-0 top-0 w-1/2 h-full bg-white rounded-l-lg page-shadow-left"
                    style={{
                      transformOrigin: 'right center',
                      transformStyle: 'preserve-3d',
                      background: 'linear-gradient(to right, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                      zIndex: 1
                    }}
                  >
                    <div className="p-6 md:p-8 h-full flex flex-col">
                      <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                        {pages[currentPage + 2].content.title}
                      </h2>
                      <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                        {pages[currentPage + 2].content.text}
                      </p>
                      <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                        {pages[currentPage + 2].content.pageNumber}
                      </div>
                    </div>
                  </div>

                  {/* Next Right Page */}
                  {pages[currentPage + 3] && (
                    <div
                      className="absolute right-0 top-0 w-1/2 h-full bg-white rounded-r-lg page-shadow-right"
                      style={{
                        transformOrigin: 'left center',
                        transformStyle: 'preserve-3d',
                        background: 'linear-gradient(to left, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                        zIndex: 1
                      }}
                    >
                      <div className="p-6 md:p-8 h-full flex flex-col">
                        <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                          {pages[currentPage + 3].content.title}
                        </h2>
                        <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                          {pages[currentPage + 3].content.text}
                        </p>
                        <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                          {pages[currentPage + 3].content.pageNumber}
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Previous Pages (shown during flip) */}
              {isFlipping && flipDirection === 'prev' && currentPage > 1 && (
                <>
                  {/* Previous Left Page */}
                  {pages[currentPage - 2] && (
                    <div
                      className="absolute left-0 top-0 w-1/2 h-full bg-white rounded-l-lg page-shadow-left"
                      style={{
                        transformOrigin: 'right center',
                        transformStyle: 'preserve-3d',
                        background: 'linear-gradient(to right, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                        zIndex: 1
                      }}
                    >
                      <div className="p-6 md:p-8 h-full flex flex-col">
                        <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                          {pages[currentPage - 2].content.title}
                        </h2>
                        <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                          {pages[currentPage - 2].content.text}
                        </p>
                        <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                          {pages[currentPage - 2].content.pageNumber}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Previous Right Page */}
                  <div
                    className="absolute right-0 top-0 w-1/2 h-full bg-white rounded-r-lg page-shadow-right"
                    style={{
                      transformOrigin: 'left center',
                      transformStyle: 'preserve-3d',
                      background: 'linear-gradient(to left, #fefefe 0%, #ffffff 95%, #f8f8f8 100%)',
                      zIndex: 1
                    }}
                  >
                    <div className="p-6 md:p-8 h-full flex flex-col">
                      <h2 className="text-xl md:text-3xl font-bold mb-4 md:mb-6 text-gray-800 font-serif">
                        {pages[currentPage - 1].content.title}
                      </h2>
                      <p className="text-gray-700 leading-relaxed flex-1 text-sm md:text-base font-serif">
                        {pages[currentPage - 1].content.text}
                      </p>
                      <div className="text-center text-gray-500 text-sm mt-4 font-serif">
                        {pages[currentPage - 1].content.pageNumber}
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Center Binding */}
              <div className="absolute left-1/2 top-0 w-2 h-full bg-gradient-to-r from-gray-300 via-gray-400 to-gray-300 z-20 shadow-inner" style={{ transform: 'translateX(-50%)' }}></div>
            </div>
          )}
        </div>

        {/* Navigation Controls */}
        {isBookOpen && (
          <div className="absolute -bottom-16 left-1/2 flex gap-4" style={{ transform: 'translateX(-50%)' }}>
            <button
              onClick={prevPage}
              disabled={currentPage <= 1 || isFlipping}
              className="px-4 md:px-6 py-2 md:py-3 bg-amber-600 text-white rounded-lg shadow-lg hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-sm md:text-base"
              style={{ transition: 'background-color 0.2s ease-in-out' }}
            >
              ← Previous
            </button>
            <button
              onClick={nextPage}
              disabled={currentPage >= pages.length - 2 || isFlipping}
              className="px-4 md:px-6 py-2 md:py-3 bg-amber-600 text-white rounded-lg shadow-lg hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-sm md:text-base"
              style={{ transition: 'background-color 0.2s ease-in-out' }}
            >
              Next →
            </button>
          </div>
        )}

        {/* Page Indicator */}
        {isBookOpen && (
          <div className="absolute -top-8 left-1/2 text-gray-600 text-sm font-serif" style={{ transform: 'translateX(-50%)' }}>
            Pages {currentPage} - {currentPage + 1} of {pages.length - 1}
          </div>
        )}
      </div>
    </div>
    </>
  );
}
