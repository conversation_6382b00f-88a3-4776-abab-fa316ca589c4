'use client';
import React, { useState } from 'react';

const FlipBook = () => {
  const [currentPage, setCurrentPage] = useState('page-1');

  const handlePageChange = (pageId) => {
    setCurrentPage(pageId);
  };

  return (
    <>
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css?family=Cormorant+Garamond:300,400,600|Tulpen+One&display=swap');

        :root {
          --body-bg: #fafafa;
          --page-bg: #f5f5f5;
          --dark-text: #2a2935;
          --baseline: 12px;
          --book-title: 'Tulpen One', sans-serif;
          --title: 'Cormorant Garamond', serif;
          --body: 'Cormorant Garamond', serif;
          --base-size: calc(var(--baseline) * 1.2);
        }

        * {
          box-sizing: border-box;
        }

        .cover {
          width: calc(var(--baseline) * 60);
          height: calc(var(--baseline) * 42.6);
          box-shadow: 0 0 100px rgba(0, 0, 0, 0.3);
        }

        .book {
          width: 100%;
          height: 100%;
          display: flex;
          perspective: 1200px;
        }

        .book__page {
          position: relative;
          width: 50%;
          height: 100%;
          display: grid;
          transform: rotateY(0deg);
          transition: transform 0.9s cubic-bezier(0.645, 0.045, 0.355, 1);
          transform-origin: 0% 0%;
          background-color: var(--page-bg);
          background-image: linear-gradient(
            90deg,
            rgba(227, 227, 227, 1) 0%,
            rgba(247, 247, 247, 0) 18%
          );
          cursor: pointer;
        }

        .book__page:nth-of-type(1) {
          background-image: linear-gradient(
            -90deg,
            rgba(227, 227, 227, 1) 0%,
            rgba(247, 247, 247, 0) 18%
          );
        }

        .book__page--1 {
          overflow: hidden;
        }

        .book__page--1 img {
          width: 100%;
          max-width: 100%;
          height: auto;
        }

        .book__page--2 {
          position: absolute;
          right: 0;
          pointer-events: none;
          transform-style: preserve-3d;
          background-color: var(--page-bg);
          background-image: linear-gradient(
            90deg,
            rgba(227, 227, 227, 1) 0%,
            rgba(247, 247, 247, 0) 18%
          );
        }

        .book__page--4 {
          padding: 0 calc(var(--baseline) * 3);
        }

        .book__page-front {
          position: absolute;
          width: 100%;
          height: 100%;
          transform: rotateY(0deg) translateZ(1px);
        }

        .book__page-back {
          position: absolute;
          width: 100%;
          height: 100%;
          padding: 0 calc(var(--baseline) * 1.8);
          transform: rotateY(180deg) translateZ(1px);
        }

        .page__content {
          padding: var(--baseline);
          height: 100%;
          position: relative;
          text-align: center;
        }

        .page__content-book-title {
          font-family: var(--book-title);
          font-size: calc(var(--base-size) * 3);
          font-weight: bold;
          text-transform: uppercase;
          letter-spacing: 3px;
          color: var(--dark-text);
          margin-top: calc(var(--baseline) * 5);
          margin-bottom: calc(var(--baseline) * 2);
        }

        .page__content-author {
          font-family: var(--title);
          font-size: calc(var(--base-size) * 1.2);
          font-weight: 100;
          text-transform: uppercase;
          color: var(--dark-text);
          border-top: 1px solid var(--dark-text);
          border-bottom: 1px solid var(--dark-text);
          display: inline-block;
          padding: calc(var(--baseline) / 2) calc(var(--baseline) / 5);
          margin-bottom: calc(var(--baseline) * 6);
        }

        .page__content-credits {
          font-family: var(--title);
          text-transform: uppercase;
          font-size: calc(var(--base-size) * 0.8);
          margin-bottom: calc(var(--baseline) * 2);
          letter-spacing: 1px;
        }

        .page__content-credits span {
          display: block;
          font-size: calc(var(--base-size) * 1.2);
          letter-spacing: 0;
        }

        .page__content-copyright {
          position: absolute;
          width: calc(100% - (var(--baseline) * 2));
          bottom: calc(var(--baseline) * 2);
          font-family: var(--title);
          font-size: calc(var(--base-size) * 0.8);
          text-transform: uppercase;
        }

        .page__content-title {
          font-family: var(--title);
          font-size: calc(var(--base-size) * 1);
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-top: calc(var(--baseline) * 5);
          margin-bottom: calc(var(--baseline) * 3);
        }

        .page__content-blockquote {
          margin-bottom: calc(var(--baseline) * 2);
        }

        .page__content-blockquote-text {
          font-family: var(--title);
          font-size: calc(var(--base-size) * 0.67);
          font-style: italic;
          text-align: justify;
        }

        .page__content-blockquote-reference {
          font-family: var(--title);
          font-size: calc(var(--base-size) * 0.7);
          margin-top: calc(var(--baseline) * 0.3);
          float: right;
          text-transform: uppercase;
        }

        .page__content-text {
          font-family: var(--title);
          font-size: calc(var(--base-size) * 0.67);
          text-align: justify;
          text-indent: var(--baseline);
        }

        .page__number {
          position: absolute;
          bottom: var(--baseline);
          width: calc(100% - (var(--baseline) * 2));
          font-family: var(--title);
          font-size: calc(var(--base-size) * 0.67);
          text-align: center;
        }

        .book__page--flipped {
          transform: rotateY(-180deg);
        }

        .book__page--2.book__page--flipped {
          pointer-events: auto;
        }
      `}</style>

      <div className="cover">
        <div className="book">
          <div
            className={`book__page book__page--1 ${
              currentPage === 'page-2' ? 'book__page--flipped' : ''
            }`}
            onClick={() => handlePageChange('page-2')}
          >
            <img
              src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/193203/1111.jpg"
              alt="Book Cover"
            />
          </div>

          <div
            className={`book__page book__page--4 ${
              currentPage === 'page-1' ? '' : 'book__page--flipped'
            }`}
            onClick={() => handlePageChange('page-1')}
          >
            <div className="page__content">
              <h1 className="page__content-title">I</h1>
              <div className="page__content-blockquote">
                <p className="page__content-blockquote-text">
                  HARI SELDON — . . . born in the 11,988th year of the Galactic
                  Era; died 12,069. The dates are more commonly given in terms
                  of the current Foundational Era as -79 to the year 1 F.E. Born
                  to middle-class parents on Helicon, Arcturus sector (where his
                  father, in a legend of doubtful authenticity, was a tobacco
                  grower in the hydroponic plants of the planet), he early
                  showed amazing ability in mathematics. Anecdotes concerning
                  his ability are innumerable, and some are contradictory. At
                  the age of two, he is said to have. . .
                </p>
                <p className="page__content-blockquote-text">
                  . . . Undoubtedly his greatest contributions were in the field
                  of psychohistory. Seldon found the field little more than a
                  set of vague axioms; he left it a profound statistical
                  science. . . .
                </p>
                <p className="page__content-blockquote-text">
                  . . . The best existing authority we have for the details of
                  his life is the biography written by Gaal Dornick who, as a
                  young man, met Seldon two years before the great
                  mathematician's death. The story of the meeting . . .
                </p>
                <span className="page__content-blockquote-reference">
                  Encyclopedia Galactica*
                </span>
              </div>
              <div className="page__content-text">
                <p>
                  His name was Gaal Dornick and he was just a country boy who
                  had never seen Trantor before. That is, not in real life. He
                  had seen it many times on the hyper-video, and occasionally in
                  tremendous three-dimensional newscasts covering an Imperial
                  Coronation or the opening of a Galactic Council. Even though
                  he had lived all his life on the world of Synnax, which
                  circled a star at the edges of the Blue Drift, he was not cut
                  off from civilization, you see. At that time, no place in the
                  Galaxy was.
                </p>
                <p>
                  There were nearly twenty-five million inhabited planets in the
                  Galaxy then, and not one but owed allegiance to the Empire
                  whose seat was on Trantor. It was the last half-century in
                  which that could be said.
                </p>
                <p>
                  To Gaal, this trip was the undoubted climax of his young,
                  scholarly life. He had been in space before so that the trip,
                  as a voyage and nothing more, meant little to him. To be sure,
                  he had traveled previously only as far as Synnax's only
                  satellite in order to get the data on the mechanics of meteor
                  driftage which he needed for his dissertation, but
                  space-travel was all one whether one travelled half a million
                  miles, or as many light years.
                </p>
              </div>
              <div className="page__number">3</div>
            </div>
          </div>

          <div
            className={`book__page book__page--2 ${
              currentPage === 'page-2' ? 'book__page--flipped' : ''
            }`}
          >
            <div className="book__page-front">
              <div className="page__content">
                <h1 className="page__content-book-title">Foundation</h1>
                <h2 className="page__content-author">Isaac Asimov</h2>

                <p className="page__content-credits">
                  Introduction by
                  <span>Paul Krugman</span>
                </p>

                <p className="page__content-credits">
                  Illustrations by
                  <span>Alex Wells</span>
                </p>

                <div className="page__content-copyright">
                  <p>The Folio Society</p>
                  <p>London - MMXII</p>
                </div>
              </div>
            </div>
            <div className="book__page-back">
              <div className="page__content">
                <h1 class="page__content-title">Contents</h1>
                <table class="page__content-table">
                  <tr>
                    <td align="left">Part I</td>
                    <td align="left">The Psycohistorians</td>
                    <td align="right">3</td>
                  </tr>
                  <tr>
                    <td align="left">Part II</td>
                    <td align="left">The Encyclopedists</td>
                    <td align="right">43</td>
                  </tr>
                  <tr>
                    <td align="left">Part III</td>
                    <td align="left">The Mayors</td>
                    <td align="right">87</td>
                  </tr>
                  <tr>
                    <td align="left">Part IV</td>
                    <td align="left">The Traders</td>
                    <td align="right">147</td>
                  </tr>
                  <tr>
                    <td align="left">Part V</td>
                    <td align="left">The Merchant Princes</td>
                    <td align="right">173</td>
                  </tr>
                </table>

                <div class="page__number">2</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FlipBook;
